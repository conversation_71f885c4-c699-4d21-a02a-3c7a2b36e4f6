"""
Data transformation utilities for neural data analysis.

This module provides a registry-based system for data transformations that can be
applied to neural datasets. Transformations are registered by name and can be
chained together in pipelines.

The module supports:
- Registry-based transform functions
- Pipeline composition of multiple transforms
- Configurable transforms with parameters
"""

import torch
from typing import Callable, Dict, List, Any

# ──────────────────────────────────────────────────────────────────────────────
# 1.  Transform registry
# ──────────────────────────────────────────────────────────────────────────────
class TransformFn(Callable[[torch.Tensor], torch.Tensor]): ...
TRANSFORM_REGISTRY: Dict[str, Callable[[Dict[str, Any]], TransformFn]] = {}

def _register(name):
    def wrap(fn):
        TRANSFORM_REGISTRY[name] = fn
        return fn
    return wrap

@_register("pixelnorm")
def _make_pixelnorm(cfg):
    def pixelnorm(x: torch.Tensor):
        return (x.float() - 127) / 255
    return pixelnorm

@_register("diff")
def _make_diff(cfg):
    axis = cfg.get("axis", 0)
    def diff(x: torch.Tensor):
        # prepend first slice to keep length constant
        prepend = x.index_select(axis, torch.tensor([0], device=x.device))
        return torch.diff(x, dim=axis, prepend=prepend)
    return diff

@_register("mul")
def _make_mul(cfg):
    factor = cfg if isinstance(cfg, (int,float)) else cfg.get("factor", 1.0)
    def mul(x): return x * factor
    return mul

@_register("temporal_basis")
def _make_basis(cfg):
    from DataYatesV1.models.modules import TemporalBasis
    basis = TemporalBasis(**cfg)
    def tb(x):                         # x (T, …)  or (B,T, …)
        # TemporalBasis expects (B,C,T); reshape accordingly
        orig_shape = x.shape

        if x.ndim == 2:                # (T, C) → (1,C,T)
            x = x.transpose(0,1).unsqueeze(0)
        elif x.ndim == 3:              # (T, H, W) → (1, 1, T, H, W)
            x = x.unsqueeze(0).unsqueeze(0)
        else:
            raise ValueError("Unsupported tensor rank for temporal_basis")

        y = basis(x)                    # (B,C',T)
        if len(orig_shape) == 2:                # (1,C',T) → (T,C')
            y = y.permute(0,2,1).squeeze(0) # back to (T,C')
        elif len(orig_shape) == 3:  
            # (1, C, T, H, W) → (T, C, H, W)
            y = y.permute(2,0,1,3,4).reshape((orig_shape[0], -1, orig_shape[1], orig_shape[2])) # to (T, Cnew, H, W)

        return y.view(*y.shape)         # torchscript friendliness
    return tb

@_register("splitrelu")
def _make_splitrelu(cfg):
    from DataYatesV1.models.modules import SplitRelu
    return SplitRelu(**cfg)

@_register("symlog")
def _make_symlog(cfg):
    def symlog(x):
        return torch.sign(x) * torch.log1p(torch.abs(x))
    return symlog

@_register("maxnorm")
def _make_maxnorm(cfg):
    def maxnorm(x):
        return x / torch.max(torch.abs(x))
    return maxnorm

@_register("dacones")
def _make_dacones(cfg):
    from DataYatesV1.models.modules import DAModel
    def dacones(x):
        cones = DAModel(**cfg)
        # permute (B,H,W) to (1, B, H, W) and squeeze back to (B,H,W)
        return cones(x.unsqueeze(0)).squeeze(0).permute(1,0,2,3)

    return dacones

@_register("to")
def _make_to(cfg):
    """
    Transform to convert tensor to a different dtype.

    Args:
        cfg: Either a string (dtype name) or dict with 'dtype' key
             Supported dtypes: 'float16', 'float32', 'float64', 'int8', 'int16', 'int32', 'int64', etc.
    """
    if isinstance(cfg, str):
        dtype_str = cfg
    else:
        dtype_str = cfg.get("dtype", "float32")

    # Map string names to torch dtypes
    dtype_map = {
        'float16': torch.float16,
        'float32': torch.float32,
        'float64': torch.float64,
        'int8': torch.int8,
        'int16': torch.int16,
        'int32': torch.int32,
        'int64': torch.int64,
        'bool': torch.bool,
        'uint8': torch.uint8,
    }

    if dtype_str not in dtype_map:
        raise ValueError(f"Unsupported dtype '{dtype_str}'. Supported: {list(dtype_map.keys())}")

    target_dtype = dtype_map[dtype_str]

    def to_dtype(x: torch.Tensor):
        return x.to(dtype=target_dtype)

    return to_dtype



# ──────────────────────────────────────────────────────────────────────────────
# 2.  Build a composite transform pipeline
# ──────────────────────────────────────────────────────────────────────────────
def make_pipeline(op_list: List[Dict[str, Any]]) -> TransformFn:
    fns: List[TransformFn] = []
    for op_dict in op_list:
        name, cfg = next(iter(op_dict.items()))
        if name not in TRANSFORM_REGISTRY:
            raise ValueError(f"Unknown transform '{name}'")
        fns.append(TRANSFORM_REGISTRY[name](cfg))

    def pipeline(x):
        for fn in fns:
            x = fn(x)
        return x
    return pipeline
